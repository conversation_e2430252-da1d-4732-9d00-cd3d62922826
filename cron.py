import traceback
from app import crawl_and_predict
import mysql.connector
from datetime import datetime, timedelta

def cron_once():
    # 一次性的定时任务

    tweet_ids = get_task_from_db()
    for tweet_id in tweet_ids:

        try:
            result = crawl_and_predict(tweet_id, refresh=False)
        except Exception as e:
            detailed_error_traceback = traceback.format_exc()
            print("\n--- Cron Job Error ---")
            print(detailed_error_traceback)
            print("---------------------------\n")


def get_task_from_db():
    # 从数据库中获取任务
    # 返回一个包含tweet_id的列表

    # MySQL Configuration (same as in app.py)
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': '123456',
        'database': 'cas',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }

    try:
        # Get current time and calculate one day ago
        now_time = datetime.now()
        one_day_ago = now_time - timedelta(days=1)

        # Connect to database
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()

        # Query for tasks that are older than one day and not crawled yet
        query = """
        SELECT tweet_id
        FROM tweet_task
        WHERE created_at < %s AND has_crawled = 0
        """

        cursor.execute(query, (one_day_ago,))
        results = cursor.fetchall()

        # Extract tweet_ids from results
        tweet_ids = [row[0] for row in results]

        cursor.close()
        conn.close()

        return tweet_ids

    except Exception as e:
        print(f"Error querying database: {e}")
        return []
